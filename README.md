# Pebl

A smart personal assistant iOS app that helps you organize your thoughts and tasks (pebls) into meaningful categories.

## Features

- Automatically categorizes pebls into categories
- Default categories include:
  - To-Read
  - Shopping
  - To-Do
  - Movies to Watch
  - Appointments
- Clean and intuitive SwiftUI interface
- AI-powered pebl categorization

## Requirements

- iOS device or simulator
- Xcode 15.0+
- Swift 5.0+

## Installation

1. Clone the repository
2. Open `Pebl.xcodeproj` in Xcode
3. Build and run the project

## Usage

1. Enter your message in the text field
2. Tap "Store" to let Pebl organize it
3. View your categorized items by selecting the respective category

## License

This project is proprietary software. All rights reserved.
