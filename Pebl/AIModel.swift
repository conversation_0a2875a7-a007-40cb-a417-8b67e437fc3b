import Foundation

class AIModel {

    // MARK: - Simple Configuration

    private let subfolderPatterns: [String: [String: [String]]] = [
        "movies": [
            "Comedy": ["comedy", "funny", "laugh"],
            "Action & Adventure": ["action", "adventure", "thriller"],
            "Drama & Romance": ["drama", "romantic", "romance"],
            "Horror & Thriller": ["horror", "scary"],
            "Documentaries": ["documentary", "docu"]
        ],
        "shopping": [
            "Clothing & Fashion": ["cloth", "shirt", "dress", "shoe", "fashion"],
            "Electronics": ["electronic", "phone", "computer", "gadget", "tech"],
            "Books": ["book", "read"],
            "Food & Grocery": ["food", "grocery", "kitchen"],
            "Home & Garden": ["home", "furniture", "decor"]
        ],
        "tasks": [
            "Urgent": ["urgent", "asap", "important"],
            "Work": ["work", "office", "meeting"],
            "Personal": ["personal", "home", "family"]
        ],
        "reading": [
            "Articles & Blogs": ["article", "blog", "news"],
            "Books": ["book", "novel"],
            "Research & Papers": ["research", "paper", "study"]
        ]
    ]

    // MARK: - Main Functions

    func categorizeMessage(_ message: String, availableCategories: [String]) -> String {
        // Edge case: Empty or invalid inputs
        guard !availableCategories.isEmpty else { return "Uncategorized" }
        guard !message.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return availableCategories.first ?? "Uncategorized"
        }

        let cleanMessage = message.trimmingCharacters(in: .whitespacesAndNewlines)

        // Edge case: Very short or ambiguous messages - use fallback logic
        if cleanMessage.count <= 2 || isAmbiguousMessage(cleanMessage) {
            return handleAmbiguousMessage(cleanMessage, availableCategories: availableCategories)
        }

        // Analyze URLs for context
        let urls = extractURLsFromMessage(message)
        let urlContext = urls.isEmpty ? "" : analyzeURLContent(urls[0])

        let urlAnalysis = urlContext.isEmpty ? "" : "\n\nURL Analysis: This message contains a URL related to: \(urlContext)"

        let prompt = """
        Categorize this message. You can either:
        1. Choose from existing categories: \(availableCategories.joined(separator: ", "))
        2. Create a NEW category if this represents a major life project/area

        RULES:
        1. STRONGLY prefer existing categories (90% of the time)
        2. For movies: use "Movies to Watch" or similar entertainment category
        3. For shopping: use "Shopping" unless more specific category exists
        4. For tasks: use "To-Do" unless more specific category exists
        5. For reading: use "To-Read" unless more specific category exists
        6. Only create NEW category for major life projects like "Wedding Planning", "House Buying", "Book Writing"

        If creating new category, make it:
        - Broad enough for future similar items (think "Book Writing" not "Chapter 3 Notes")
        - Clear and descriptive ("House Buying", "Baby Planning", "Career Change")
        - Properly capitalized

        Return ONLY the category name, nothing else.\(urlAnalysis)

        Message: \(message)
        """

        let aiResponse = makeAIRequest(prompt: prompt, maxTokens: 30)
        return validateCategoryResponse(aiResponse, message: cleanMessage, availableCategories: availableCategories)
    }

    func shouldCreateSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        // Only count messages in the current category, excluding subcategories
        let messageCount = category.messages.count
        guard messageCount >= CategorizationEngine.CategorizationConfig.default.recategorizationThreshold else { return (false, nil) }

        let categoryName = category.name.lowercased()
        let messageLower = message.lowercased()

        // Check simple patterns
        for (categoryType, patterns) in subfolderPatterns {
            if categoryName.contains(categoryType) ||
               (categoryType == "movies" && (categoryName.contains("movie") || categoryName.contains("film") || categoryName.contains("watch"))) ||
               (categoryType == "tasks" && (categoryName.contains("to-do") || categoryName.contains("todo") || categoryName.contains("task"))) ||
               (categoryType == "reading" && categoryName.contains("read")) {

                for (subfolder, keywords) in patterns {
                    if keywords.contains(where: { messageLower.contains($0) }) {
                        return (true, subfolder)
                    }
                }
            }
        }

        // If no pattern match, ask AI for dynamic suggestion
        return suggestDynamicSubfolder(for: message, in: category)
    }

    func getSFSymbolForCategory(_ category: String) -> String {
        let prompt = "What SF Symbol best represents the category '\(category)'? Return only the symbol name (like 'cart.fill' or 'book.circle')."
        let response = makeAIRequest(prompt: prompt, maxTokens: 10) ?? "folder.fill"

        // Clean the response by removing quotes and trimming whitespace
        let cleanedResponse = response
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .trimmingCharacters(in: CharacterSet(charactersIn: "\"'`"))

        // Ensure we never return an empty string or invalid symbol
        return cleanedResponse.isEmpty ? "folder.fill" : cleanedResponse
    }

    // MARK: - URL Analysis

    func extractURLsFromMessage(_ message: String) -> [String] {
        let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue)
        let matches = detector?.matches(in: message, options: [], range: NSRange(location: 0, length: message.utf16.count))

        return matches?.compactMap { match in
            if let range = Range(match.range, in: message) {
                return String(message[range])
            }
            return nil
        } ?? []
    }

    func analyzeURLContent(_ url: String) -> String {
        let domain = URL(string: url)?.host ?? url
        let prompt = "Categorize this URL: \(url) (domain: \(domain)). Return one of: shopping, entertainment, news, learning, work, health, travel, food, technology, lifestyle, web_link"
        return makeAIRequest(prompt: prompt, maxTokens: 10) ?? "web_link"
    }

    func extractTitleFromURL(_ url: String, categoryType: String) -> String {
        let domain = URL(string: url)?.host ?? "unknown"

        let prompt = """
        Extract a meaningful, human-readable title from this URL for display in a \(categoryType) category.

        URL: \(url)
        Domain: \(domain)

        Rules:
        1. For movies/entertainment: Extract movie/show title (e.g., "The Matrix", "Breaking Bad Season 3")
        2. For shopping: Extract product name (e.g., "iPhone 15 Pro", "Nike Air Max Shoes")
        3. For articles/news: Extract article headline or main topic
        4. For general links: Extract page title or main subject
        5. Keep it concise (max 6 words)
        6. Make it descriptive and user-friendly
        7. If you can't determine a good title, return "Unknown"

        Return ONLY the title, nothing else.
        """

        let response = makeAIRequest(prompt: prompt, maxTokens: 50) ?? "Unknown"
        let cleanTitle = response.trimmingCharacters(in: .whitespacesAndNewlines)

        // Validate the response
        if cleanTitle.isEmpty || cleanTitle.lowercased().contains("unknown") || cleanTitle.lowercased().contains("error") {
            return "Unknown"
        }

        return cleanTitle
    }

    // MARK: - Edge Case Handling & Validation

    private func isAmbiguousMessage(_ message: String) -> Bool {
        let ambiguousPatterns = [
            "^[0-9]+$",                    // Just numbers like "7", "123"
            "^[a-zA-Z]$",                  // Single letters like "a", "B"
            "^[a-zA-Z]{1,3}$",            // Very short words like "hi", "ok", "yes"
            "^[!@#$%^&*()_+\\-=\\[\\]{}|;':\",./<>?]+$", // Just symbols
            "^\\s*$"                       // Just whitespace
        ]

        return ambiguousPatterns.contains { pattern in
            message.range(of: pattern, options: .regularExpression) != nil
        }
    }

    private func handleAmbiguousMessage(_ message: String, availableCategories: [String]) -> String {
        // For very short/ambiguous messages, use smart fallbacks
        let messageLower = message.lowercased()

        // Check for common short patterns
        if messageLower.contains("buy") || messageLower.contains("get") || messageLower.contains("order") {
            return findBestCategory(["Shopping", "To-Do"], from: availableCategories)
        }

        if messageLower.contains("watch") || messageLower.contains("movie") || messageLower.contains("film") {
            return findBestCategory(["Movies to Watch", "To-Read", "Entertainment"], from: availableCategories)
        }

        if messageLower.contains("read") || messageLower.contains("book") {
            return findBestCategory(["To-Read", "Books"], from: availableCategories)
        }

        // For pure numbers or single letters, default to To-Do or first category
        return findBestCategory(["To-Do", "Uncategorized"], from: availableCategories)
    }

    private func findBestCategory(_ preferredCategories: [String], from availableCategories: [String]) -> String {
        for preferred in preferredCategories {
            // Exact match
            if availableCategories.contains(preferred) {
                return preferred
            }

            // Partial match (case insensitive)
            if let match = availableCategories.first(where: {
                $0.lowercased().contains(preferred.lowercased()) ||
                preferred.lowercased().contains($0.lowercased())
            }) {
                return match
            }
        }

        return availableCategories.first ?? "Uncategorized"
    }

    private func validateCategoryResponse(_ response: String?, message: String, availableCategories: [String]) -> String {
        guard let response = response?.trimmingCharacters(in: .whitespacesAndNewlines)
                                  .trimmingCharacters(in: CharacterSet(charactersIn: "\"'`")),
              !response.isEmpty else {
            return findBestCategory(["To-Do", "Uncategorized"], from: availableCategories)
        }

        // Remove quotes, periods, and other punctuation
        let cleanResponse = response
            .replacingOccurrences(of: "\"", with: "")
            .replacingOccurrences(of: "'", with: "")
            .replacingOccurrences(of: ".", with: "")
            .replacingOccurrences(of: "!", with: "")
            .replacingOccurrences(of: "?", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if response looks like an AI explanation/error rather than a category
        if isAIExplanationResponse(cleanResponse) {
            print("⚠️ AI returned explanation instead of category for message: '\(message)'")
            return handleAmbiguousMessage(message, availableCategories: availableCategories)
        }

        // Check for exact match with existing categories (case insensitive)
        if let exactMatch = availableCategories.first(where: {
            $0.lowercased() == cleanResponse.lowercased()
        }) {
            return exactMatch
        }

        // Check for partial match with existing categories
        if let partialMatch = availableCategories.first(where: {
            $0.lowercased().contains(cleanResponse.lowercased()) ||
            cleanResponse.lowercased().contains($0.lowercased())
        }) {
            return partialMatch
        }

        // If no match with existing categories, validate if it's a reasonable new category
        if isValidNewCategory(cleanResponse, message: message) {
            return cleanResponse
        }

        // If response doesn't look like a valid category, use fallback
        print("⚠️ Invalid category response '\(cleanResponse)' for message: '\(message)'")
        return findBestCategory(["To-Do", "Uncategorized"], from: availableCategories)
    }

    private func isAIExplanationResponse(_ response: String) -> Bool {
        // Check for common AI explanation patterns
        let explanationPatterns = [
            "The provided",
            "I cannot",
            "insufficient",
            "not enough",
            "unclear",
            "not sufficient",
            "unable to",
            "difficult to",
            "hard to",
            "cannot determine",
            "more context",
            "more information",
            "ambiguous"
        ]

        let responseLower = response.lowercased()

        // If response is too long, it's likely an explanation
        if response.count > 50 {
            return true
        }

        // Check for explanation keywords
        for pattern in explanationPatterns {
            if responseLower.contains(pattern.lowercased()) {
                return true
            }
        }

        return false
    }

    private func isValidNewCategory(_ categoryName: String, message: String) -> Bool {
        // Category name should be reasonable length
        guard categoryName.count >= 3 && categoryName.count <= 30 else {
            return false
        }

        // Should not contain common explanation words
        if isAIExplanationResponse(categoryName) {
            return false
        }

        // Should not be just numbers or single letters
        if isAmbiguousMessage(categoryName) {
            return false
        }

        // Should contain mostly letters and spaces
        let allowedCharacters = CharacterSet.letters.union(.whitespaces).union(.punctuationCharacters)
        let categoryCharacters = CharacterSet(charactersIn: categoryName)
        if !categoryCharacters.isSubset(of: allowedCharacters) {
            return false
        }

        // Should not be a common word that's not a category
        let nonCategoryWords = ["yes", "no", "maybe", "ok", "okay", "sure", "fine", "good", "bad", "help", "please", "thanks"]
        if nonCategoryWords.contains(categoryName.lowercased()) {
            return false
        }

        return true
    }

    // MARK: - Dynamic Subfolder Suggestion

    private func suggestDynamicSubfolder(for message: String, in category: Category) -> (shouldCreate: Bool, subfolderName: String?) {
        let existingMessages = category.messages.prefix(5).map { $0.text }.joined(separator: "; ")

        let prompt = """
        Should this message go in a subfolder within "\(category.name)"?

        New message: \(message)
        Existing messages: \(existingMessages)

        If yes, suggest a broad subfolder name that could contain multiple similar messages.
        If no, respond with "NO_SUBFOLDER".

        Examples: "Research", "Planning", "Shopping", "Medical", "Work Tasks"

        Response:
        """

        let response = makeAIRequest(prompt: prompt, maxTokens: 20) ?? "NO_SUBFOLDER"

        // Clean the response by removing quotes and trimming whitespace
        let cleanedResponse = response
            .trimmingCharacters(in: .whitespacesAndNewlines)
            .trimmingCharacters(in: CharacterSet(charactersIn: "\"'`"))
            .replacingOccurrences(of: "\"", with: "")
            .replacingOccurrences(of: "'", with: "")

        if cleanedResponse == "NO_SUBFOLDER" || cleanedResponse.isEmpty {
            return (false, nil)
        } else {
            return (true, cleanedResponse)
        }
    }

    // MARK: - AI Request Helper

    private func makeAIRequest(prompt: String, maxTokens: Int) -> String? {
        guard let apiKey = ProcessInfo.processInfo.environment["OPENAI_API_KEY"] else {
            print("⚠️ Warning: OpenAI API key not found. Using fallback categorization.")
            return nil
        }

        guard let url = URL(string: "https://api.openai.com/v1/chat/completions") else {
            print("⚠️ Warning: Invalid OpenAI API URL")
            return nil
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 15.0  // Reduced timeout for better UX

        let parameters: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "system", "content": "You are a precise categorization assistant. Always respond with ONLY the exact category name from the provided list. Never provide explanations or additional text."],
                ["role": "user", "content": prompt]
            ],
            "max_tokens": maxTokens,
            "temperature": 0.1  // Lower temperature for more consistent responses
        ]

        guard let jsonData = try? JSONSerialization.data(withJSONObject: parameters, options: []) else {
            print("⚠️ Warning: Failed to serialize AI request")
            return nil
        }

        request.httpBody = jsonData

        let semaphore = DispatchSemaphore(value: 0)
        var result: String?
        var requestError: Error?

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            defer { semaphore.signal() }

            if let error = error {
                requestError = error
                return
            }

            guard let httpResponse = response as? HTTPURLResponse else {
                print("⚠️ Warning: Invalid HTTP response")
                return
            }

            guard httpResponse.statusCode == 200 else {
                print("⚠️ Warning: AI API returned status code: \(httpResponse.statusCode)")
                return
            }

            guard let data = data else {
                print("⚠️ Warning: No data received from AI API")
                return
            }

            do {
                guard let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] else {
                    print("⚠️ Warning: Invalid JSON response from AI API")
                    return
                }

                if let error = json["error"] as? [String: Any],
                   let message = error["message"] as? String {
                    print("⚠️ Warning: AI API error: \(message)")
                    return
                }

                guard let choices = json["choices"] as? [[String: Any]],
                      let firstChoice = choices.first,
                      let message = firstChoice["message"] as? [String: Any],
                      let content = message["content"] as? String else {
                    print("⚠️ Warning: Unexpected AI API response structure")
                    return
                }

                result = content.trimmingCharacters(in: .whitespacesAndNewlines)

            } catch {
                print("⚠️ Warning: Failed to parse AI API response: \(error)")
            }
        }

        task.resume()

        // Wait with timeout
        let timeoutResult = semaphore.wait(timeout: .now() + 20.0)
        if timeoutResult == .timedOut {
            print("⚠️ Warning: AI request timed out")
            task.cancel()
            return nil
        }

        if let error = requestError {
            print("⚠️ Warning: AI request failed: \(error.localizedDescription)")
            return nil
        }

        return result
    }

    // MARK: - Category Analysis

    func analyzeCategoryForOptimization(_ category: Category) -> [String] {
        guard category.messages.count >= 8 else { return [] }

        let messages = category.messages.map { $0.text }.joined(separator: "; ")
        let prompt = """
        Analyze these messages in "\(category.name)" and suggest 2-4 logical subfolders:

        Messages: \(messages)

        Suggest broad subfolder names that would help organize these messages.
        Return one name per line, or "NO_SUGGESTIONS" if no clear patterns.
        """

        let response = makeAIRequest(prompt: prompt, maxTokens: 50) ?? "NO_SUGGESTIONS"

        if response == "NO_SUGGESTIONS" {
            return []
        }

        return response.components(separatedBy: .newlines)
            .map { $0.trimmingCharacters(in: .whitespacesAndNewlines)
                    .trimmingCharacters(in: CharacterSet(charactersIn: "\"'`"))
                    .replacingOccurrences(of: "\"", with: "")
                    .replacingOccurrences(of: "'", with: "") }
            .filter { !$0.isEmpty }
    }
}
