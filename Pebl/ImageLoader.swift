//
//  ImageLoader.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import SwiftUI
import Foundation

/// Simple image loader for URL preview images
class ImageLoader: ObservableObject {
    @Published var image: UIImage?
    @Published var isLoading = false
    
    private static let cache = NSCache<NSString, UIImage>()
    private var cancellable: URLSessionDataTask?
    
    init() {
        // Configure cache
        Self.cache.countLimit = 100
        Self.cache.totalCostLimit = 50 * 1024 * 1024 // 50MB
    }
    
    func loadImage(from urlString: String) {
        print("🖼️ ImageLoader: Starting to load image from: \(urlString)")

        guard let url = URL(string: urlString) else {
            print("❌ ImageLoader: Invalid URL string: \(urlString)")
            return
        }

        // Check cache first
        let cacheKey = NSString(string: urlString)
        if let cachedImage = Self.cache.object(forKey: cacheKey) {
            print("✅ ImageLoader: Found cached image for: \(urlString)")
            self.image = cachedImage
            return
        }

        print("📡 ImageLoader: Starting network request for: \(urlString)")

        // Start loading
        isLoading = true

        cancellable = URLSession.shared.dataTask(with: url) { [weak self] data, response, error in
            DispatchQueue.main.async {
                self?.isLoading = false

                if let error = error {
                    print("❌ ImageLoader: Network error for \(urlString): \(error.localizedDescription)")
                    return
                }

                guard let data = data else {
                    print("❌ ImageLoader: No data received for: \(urlString)")
                    return
                }

                guard let uiImage = UIImage(data: data) else {
                    print("❌ ImageLoader: Could not create UIImage from data for: \(urlString)")
                    print("📊 ImageLoader: Data size: \(data.count) bytes")
                    return
                }

                print("✅ ImageLoader: Successfully loaded image for: \(urlString)")
                print("📊 ImageLoader: Image size: \(uiImage.size)")

                // Cache the image
                Self.cache.setObject(uiImage, forKey: cacheKey)
                self?.image = uiImage
            }
        }

        cancellable?.resume()
    }
    
    func cancel() {
        cancellable?.cancel()
    }
    
    deinit {
        cancel()
    }
}

/// SwiftUI view for loading and displaying URL preview images
struct URLImageView: View {
    let imageURL: String?
    let width: CGFloat
    let height: CGFloat
    
    @StateObject private var imageLoader = ImageLoader()
    
    var body: some View {
        Group {
            if let imageURL = imageURL {
                if let image = imageLoader.image {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: width, height: height)
                        .clipped()
                        .cornerRadius(8)
                } else if imageLoader.isLoading {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: width, height: height)
                        .overlay(
                            ProgressView()
                                .scaleEffect(0.8)
                        )
                } else {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.1))
                        .frame(width: width, height: height)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                                .font(.system(size: 20))
                        )
                }
            } else {
                // No image URL provided
                EmptyView()
            }
        }
        .onAppear {
            if let imageURL = imageURL {
                print("🎬 URLImageView: onAppear called with imageURL: \(imageURL)")
                imageLoader.loadImage(from: imageURL)
            } else {
                print("⚠️ URLImageView: onAppear called but imageURL is nil")
            }
        }
        .onDisappear {
            imageLoader.cancel()
        }
    }
}

/// Preview provider for testing
struct URLImageView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            URLImageView(
                imageURL: "https://via.placeholder.com/300x200",
                width: 60,
                height: 60
            )
            
            URLImageView(
                imageURL: "https://via.placeholder.com/400x300",
                width: 80,
                height: 60
            )
            
            URLImageView(
                imageURL: nil,
                width: 60,
                height: 60
            )
        }
        .padding()
    }
}
