//
//  Category.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// Represents a message with completion status and parsed content for modern UI
struct Message: Codable, Identifiable, Hashable {
    var id = UUID()
    var text: String
    var isCompleted: Bool = false

    // Parsed content for modern UI display
    var mainMessage: String
    var subcontent: String?
    var imageURL: String?

    init(text: String, isCompleted: Bool = false, categoryName: String = "") {
        self.text = text
        self.isCompleted = isCompleted

        // Check if the message is a URL and parse accordingly
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
            self.imageURL = parsed.imageURL
        } else {
            // Parse regular message for clean display
            let parsed = MessageParser.parseMessage(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
            self.imageURL = nil
        }
    }

    // Legacy initializer for existing code
    init(text: String, isCompleted: Bool = false) {
        self.text = text
        self.isCompleted = isCompleted

        // Check if the message is a URL and parse accordingly
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: "")
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
            self.imageURL = parsed.imageURL
        } else {
            // Use generic parsing for legacy messages
            let parsed = MessageParser.quickParse(text)
            self.mainMessage = parsed.main
            self.subcontent = parsed.sub
            self.imageURL = nil
        }
    }

    // Update parsed content when category changes
    mutating func updateParsedContent(for categoryName: String) {
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
            self.imageURL = parsed.imageURL
        } else {
            let parsed = MessageParser.parseMessage(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
            self.imageURL = nil
        }
    }
}

/// A hierarchical category that can contain subcategories and messages
class Category: ObservableObject, Codable, Identifiable {
    let id = UUID()
    @Published var name: String
    @Published var sfSymbol: String
    @Published var subcategories: [Category]
    @Published var messages: [Message]
    weak var parent: Category?
    weak var categoryManager: CategoryManager?
    
    init(name: String, sfSymbol: String = "folder.fill", parent: Category? = nil, categoryManager: CategoryManager? = nil) {
        self.name = name
        self.sfSymbol = sfSymbol
        self.subcategories = []
        self.messages = []
        self.parent = parent
        self.categoryManager = categoryManager
    }
    
    // MARK: - Codable Implementation
    enum CodingKeys: String, CodingKey {
        case name, sfSymbol, subcategories, messages
    }
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        name = try container.decode(String.self, forKey: .name)
        sfSymbol = try container.decode(String.self, forKey: .sfSymbol)
        subcategories = try container.decode([Category].self, forKey: .subcategories)

        // Handle both old format (array of strings) and new format (array of Message objects)
        if let messageObjects = try? container.decode([Message].self, forKey: .messages) {
            messages = messageObjects
        } else if let messageStrings = try? container.decode([String].self, forKey: .messages) {
            // Convert old string format to new Message format
            messages = messageStrings.map { Message(text: $0) }
        } else {
            messages = []
        }

        // Set parent relationships after decoding
        for subcategory in subcategories {
            subcategory.parent = self
            subcategory.categoryManager = categoryManager
        }
    }
    
    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(name, forKey: .name)
        try container.encode(sfSymbol, forKey: .sfSymbol)
        try container.encode(subcategories, forKey: .subcategories)
        try container.encode(messages, forKey: .messages)
    }
    
    // MARK: - Category Management
    
    /// Add a new subcategory
    func addSubcategory(name: String, sfSymbol: String = "folder.fill") -> Category {
        let newCategory = Category(name: name, sfSymbol: sfSymbol, parent: self, categoryManager: categoryManager)
        subcategories.append(newCategory)
        notifyManagerOfChange()
        return newCategory
    }

    /// Add a new subcategory with automatic recategorization
    func addSubcategoryWithRecategorization(name: String,
                                          sfSymbol: String = "folder.fill",
                                          completion: @escaping (SubcategoryAdditionResult) -> Void) {
        guard let categoryManager = categoryManager else {
            // Fallback to simple addition if no category manager
            let newCategory = addSubcategory(name: name, sfSymbol: sfSymbol)
            let result = SubcategoryAdditionResult(
                subcategory: newCategory,
                recategorization: RecategorizationResult(movedMessages: [], skippedCount: 0, newCategoriesCreated: []),
                success: true
            )
            completion(result)
            return
        }

        categoryManager.categorizationCoordinator.addSubcategory(
            name: name,
            sfSymbol: sfSymbol,
            to: self,
            completion: completion
        )
    }
    
    /// Add a message to this category
    func addMessage(_ messageText: String) {
        let message = Message(text: messageText, categoryName: self.name)
        messages.append(message)
        notifyManagerOfChange()
    }

    /// Add a message object to this category
    func addMessage(_ message: Message) {
        var updatedMessage = message
        updatedMessage.updateParsedContent(for: self.name)
        messages.append(updatedMessage)
        notifyManagerOfChange()
    }

    /// Remove a subcategory
    func removeSubcategory(_ category: Category) {
        subcategories.removeAll { $0.id == category.id }
        notifyManagerOfChange()
    }

    /// Remove a message by ID
    func removeMessage(withId id: UUID) {
        messages.removeAll { $0.id == id }
        notifyManagerOfChange()
    }

    /// Toggle message completion status
    func toggleMessageCompletion(withId id: UUID) {
        if let index = messages.firstIndex(where: { $0.id == id }) {
            messages[index].isCompleted.toggle()
            notifyManagerOfChange()
        }
    }

    /// Remove all completed messages
    func removeCompletedMessages() {
        messages.removeAll { $0.isCompleted }
        notifyManagerOfChange()
    }

    /// Notify the category manager that something changed
    private func notifyManagerOfChange() {
        DispatchQueue.main.async {
            self.categoryManager?.objectWillChange.send()
        }
    }
    
    /// Find a category by name (recursive search)
    func findCategory(named name: String) -> Category? {
        if self.name == name {
            return self
        }
        
        for subcategory in subcategories {
            if let found = subcategory.findCategory(named: name) {
                return found
            }
        }
        
        return nil
    }
    
    /// Get all category names in a flat array (for AI categorization)
    func getAllCategoryNames() -> [String] {
        var names = [name]
        for subcategory in subcategories {
            names.append(contentsOf: subcategory.getAllCategoryNames())
        }
        return names
    }
    
    /// Get the full path of this category (e.g., "Work/Projects/iOS")
    func getFullPath() -> String {
        if let parent = parent {
            return "\(parent.getFullPath())/\(name)"
        }
        return name
    }
    
    /// Get total count of active (non-completed) messages in this category and all subcategories
    func getTotalMessageCount() -> Int {
        let activeMessages = messages.filter { !$0.isCompleted }.count
        let subcategoryCount = subcategories.reduce(0) { $0 + $1.getTotalMessageCount() }
        return activeMessages + subcategoryCount
    }

    /// Get total count of all messages (including completed) in this category and all subcategories
    func getTotalMessageCountIncludingCompleted() -> Int {
        let subcategoryCount = subcategories.reduce(0) { $0 + $1.getTotalMessageCountIncludingCompleted() }
        return messages.count + subcategoryCount
    }

    /// Check if this category is empty (no active messages and no subcategories with active messages)
    var isEmpty: Bool {
        return getTotalMessageCount() == 0
    }
    
    /// Move all messages from this category to other categories using AI categorization
    func redistributeMessages(using aiModel: AIModel, to rootCategories: [Category]) {
        let availableCategories = rootCategories.flatMap { $0.getAllCategoryNames() }

        for message in messages {
            let targetCategoryName = aiModel.categorizeMessage(message.text, availableCategories: availableCategories)

            // Find the target category and add the message
            for rootCategory in rootCategories {
                if let targetCategory = rootCategory.findCategory(named: targetCategoryName) {
                    targetCategory.addMessage(message)
                    break
                }
            }
        }

        // Recursively redistribute messages from subcategories
        for subcategory in subcategories {
            subcategory.redistributeMessages(using: aiModel, to: rootCategories)
        }

        // Clear this category
        messages.removeAll()
        subcategories.removeAll()
    }
}

// MARK: - Category Manager

/// Manages the root categories and provides utility functions
class CategoryManager: ObservableObject {
    @Published var rootCategories: [Category] = []

    // Centralized categorization coordinator
    private(set) lazy var categorizationCoordinator = CategorizationCoordinator(categoryManager: self)

    init() {
        loadDefaultCategories()
    }
    
    private func loadDefaultCategories() {
        rootCategories = [
            Category(name: "To-Read", sfSymbol: "book.circle", categoryManager: self),
            Category(name: "Shopping", sfSymbol: "cart", categoryManager: self),
            Category(name: "To-Do", sfSymbol: "checkmark.square", categoryManager: self),
            Category(name: "Movies to Watch", sfSymbol: "film", categoryManager: self),
            Category(name: "Appointments", sfSymbol: "calendar", categoryManager: self)
        ]
    }
    
    /// Add a new root category
    func addRootCategory(name: String, sfSymbol: String = "folder.fill") -> Category {
        let newCategory = Category(name: name, sfSymbol: sfSymbol, categoryManager: self)
        rootCategories.append(newCategory)
        return newCategory
    }
    
    /// Remove a root category
    func removeRootCategory(_ category: Category) {
        rootCategories.removeAll { $0.id == category.id }
    }
    
    /// Find any category by name across all root categories
    func findCategory(named name: String) -> Category? {
        for rootCategory in rootCategories {
            if let found = rootCategory.findCategory(named: name) {
                return found
            }
        }
        return nil
    }
    
    /// Get all category names for AI categorization
    func getAllCategoryNames() -> [String] {
        return rootCategories.flatMap { $0.getAllCategoryNames() }
    }
    
    /// Categorize a message using AI and add it to the appropriate category (async)
    /// DEPRECATED: Use categorizationCoordinator.addMessage instead
    func categorizeAndAddMessage(_ message: String, using aiModel: AIModel, completion: @escaping () -> Void) {
        // Delegate to the new centralized system
        categorizationCoordinator.addMessage(message) { success in
            completion()
        }
    }

    /// Modern interface: Add a message using the centralized categorization system
    func addMessage(_ message: String, completion: @escaping (Bool) -> Void) {
        categorizationCoordinator.addMessage(message, completion: completion)
    }

    /// Batch add multiple messages using the centralized system
    func batchAddMessages(_ messages: [String], completion: @escaping (BatchAddResult) -> Void) {
        categorizationCoordinator.batchAddMessages(messages, completion: completion)
    }
}
