//
//  Message.swift
//  Pebl
//
//  Created by AI Assistant on 6/25/25.
//

import Foundation

/// Represents a message in the app
struct Message: Identifiable, Codable {
    var id = UUID()
    let text: String
    var isCompleted: Bool
    var mainMessage: String
    var subcontent: String?
    
    init(text: String, isCompleted: Bool = false, categoryName: String = "") {
        self.text = text
        self.isCompleted = isCompleted
        
        // Check if the message is a URL
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            // Parse the URL for better display
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        } else {
            // Parse regular message for clean display
            let parsed = MessageParser.parseMessage(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        }
    }
    
    /// Update the parsed content when the category changes
    mutating func updateParsedContent(for categoryName: String) {
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        }
    }
}