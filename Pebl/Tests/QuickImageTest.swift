import Foundation

// Quick test to verify Amazon image extraction is working
class QuickImageTest {
    static func runQuickTest() {
        let output = "🧪 Quick Amazon Image Test - FULL PIPELINE DEBUG\n" + String(repeating: "=", count: 50)
        print(output)
        writeToDebugFile(output)

        let testURL = "https://www.amazon.com/dp/B08N5WRWNW"
        var debugOutput = "\n🔗 Testing URL: \(testURL)"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        // Step 1: Test AI Model directly
        debugOutput = "\n📍 STEP 1: Testing AI Model directly"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let aiModel = AIModel()
        if let imageURL = aiModel.extractImageURLFromURL(testURL, categoryType: "Shopping") {
            debugOutput = "✅ AI Model SUCCESS: Image URL extracted\n📸 Image URL: \(imageURL)"
            print(debugOutput)
            writeToDebugFile(debugOutput)
        } else {
            debugOutput = "❌ AI Model FAILED: No image URL extracted"
            print(debugOutput)
            writeToDebugFile(debugOutput)
        }

        // Step 2: Test MessageParser
        debugOutput = "\n📍 STEP 2: Testing MessageParser"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let parsed = MessageParser.parseURL(testURL, for: "Shopping")
        debugOutput = "✨ Parsed Main Message: \(parsed.mainMessage)\n🔗 Parsed Subcontent: \(parsed.subcontent ?? "None")\n🖼️ Parsed Image URL: \(parsed.imageURL ?? "None")"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        // Step 3: Test Message Creation
        debugOutput = "\n📍 STEP 3: Testing Message Creation"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let message = Message(text: testURL, categoryName: "Shopping")
        debugOutput = "📝 Message Main: \(message.mainMessage)\n📝 Message Sub: \(message.subcontent ?? "None")\n📝 Message Image URL: \(message.imageURL ?? "None")\n📝 Message Original: \(message.text)"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        // Step 4: Test URL Detection
        debugOutput = "\n📍 STEP 4: Testing URL Detection Logic"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let hasHTTP = testURL.lowercased().hasPrefix("http://")
        let hasHTTPS = testURL.lowercased().hasPrefix("https://")
        debugOutput = "🔍 Has HTTP: \(hasHTTP)\n🔍 Has HTTPS: \(hasHTTPS)\n🔍 Should trigger URL parsing: \(hasHTTP || hasHTTPS)"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        // Step 5: Test a different Amazon URL
        debugOutput = "\n📍 STEP 5: Testing different Amazon URL"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let testURL2 = "https://www.amazon.com/Apple-iPhone-15-128GB-Blue/dp/B0CHX1W1XY"
        let message2 = Message(text: testURL2, categoryName: "Shopping")
        debugOutput = "📝 Message2 Main: \(message2.mainMessage)\n📝 Message2 Sub: \(message2.subcontent ?? "None")\n📝 Message2 Image URL: \(message2.imageURL ?? "None")"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        // Step 6: Test a simple non-Amazon URL
        debugOutput = "\n📍 STEP 6: Testing non-Amazon URL"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        let testURL3 = "https://www.apple.com/iphone/"
        let message3 = Message(text: testURL3, categoryName: "Shopping")
        debugOutput = "📝 Message3 Main: \(message3.mainMessage)\n📝 Message3 Sub: \(message3.subcontent ?? "None")\n📝 Message3 Image URL: \(message3.imageURL ?? "None")"
        print(debugOutput)
        writeToDebugFile(debugOutput)

        debugOutput = "\n" + String(repeating: "=", count: 50) + "\n✅ Full pipeline test completed!\n🔍 SUMMARY:\n   - Test URL 1 (Amazon): \(message.imageURL != nil ? "✅ HAS IMAGE" : "❌ NO IMAGE")\n   - Test URL 2 (Amazon): \(message2.imageURL != nil ? "✅ HAS IMAGE" : "❌ NO IMAGE")\n   - Test URL 3 (Apple): \(message3.imageURL != nil ? "✅ HAS IMAGE" : "❌ NO IMAGE")"
        print(debugOutput)
        writeToDebugFile(debugOutput)
    }

    static func writeToDebugFile(_ text: String) {
        let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
        let debugFile = documentsPath.appendingPathComponent("debug_output.txt")

        if let data = (text + "\n").data(using: .utf8) {
            if FileManager.default.fileExists(atPath: debugFile.path) {
                if let fileHandle = try? FileHandle(forWritingTo: debugFile) {
                    fileHandle.seekToEndOfFile()
                    fileHandle.write(data)
                    fileHandle.closeFile()
                }
            } else {
                try? data.write(to: debugFile)
            }
        }
    }
}
