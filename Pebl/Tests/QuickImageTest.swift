import Foundation

// Quick test to verify Amazon image extraction is working
class QuickImageTest {
    static func runQuickTest() {
        print("🧪 Quick Amazon Image Test")
        print(String(repeating: "=", count: 40))
        
        let aiModel = AIModel()
        let testURL = "https://www.amazon.com/dp/B08N5WRWNW"
        
        print("Testing URL: \(testURL)")
        
        if let imageURL = aiModel.extractImageURLFromURL(testURL, categoryType: "Shopping") {
            print("✅ SUCCESS: Image URL extracted")
            print("📸 Image URL: \(imageURL)")
        } else {
            print("❌ FAILED: No image URL extracted")
            print("⚠️  This could be due to:")
            print("   • AI service not responding")
            print("   • Network connectivity issues")
            print("   • URL validation failing")
        }
        
        print(String(repeating: "=", count: 40))
        print("Test completed!")
    }
}
