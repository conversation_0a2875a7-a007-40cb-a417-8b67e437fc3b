import SwiftUI

/// Debug view to test image rendering in the actual UI context
struct ImageRenderingDebugView: View {
    @State private var testMessage: Message?
    @State private var debugInfo: [String] = []
    @State private var showingImageDetails = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Test Message Creation Button
                    Button("Create Test Amazon Message") {
                        createTestMessage()
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    // Show the actual message view if created
                    if let message = testMessage {
                        VStack(alignment: .leading, spacing: 16) {
                            Text("📱 Actual Message View:")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            // This is how the message appears in the real app
                            ModernMessageView(message: .constant(message)) {
                                // Toggle action (empty for debug)
                            }
                            .padding()
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                            .padding(.horizontal)
                            
                            // Debug details
                            VStack(alignment: .leading, spacing: 8) {
                                Text("🔍 Debug Details:")
                                    .font(.headline)
                                
                                Text("Main Message: \(message.mainMessage)")
                                    .font(.caption)
                                
                                Text("Subcontent: \(message.subcontent ?? "None")")
                                    .font(.caption)
                                
                                Text("Image URL: \(message.imageURL ?? "None")")
                                    .font(.caption)
                                    .foregroundColor(message.imageURL != nil ? .green : .red)
                                
                                Text("Has Image URL: \(message.imageURL != nil ? "✅ YES" : "❌ NO")")
                                    .font(.caption)
                                    .fontWeight(.bold)
                                    .foregroundColor(message.imageURL != nil ? .green : .red)
                            }
                            .padding()
                            .background(Color.yellow.opacity(0.1))
                            .cornerRadius(8)
                            .padding(.horizontal)
                            
                            // Test direct image loading if URL exists
                            if let imageURL = message.imageURL {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("🖼️ Direct Image Test:")
                                        .font(.headline)
                                    
                                    Text("Testing URL: \(imageURL)")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                    
                                    URLImageView(
                                        imageURL: imageURL,
                                        width: 100,
                                        height: 100
                                    )
                                    .border(Color.red, width: 2) // Red border to see the frame
                                }
                                .padding()
                                .background(Color.green.opacity(0.1))
                                .cornerRadius(8)
                                .padding(.horizontal)
                            }
                        }
                    }
                    
                    // Debug log
                    if !debugInfo.isEmpty {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("📋 Debug Log:")
                                .font(.headline)
                                .padding(.horizontal)
                            
                            ForEach(debugInfo, id: \.self) { info in
                                Text(info)
                                    .font(.caption)
                                    .padding(.horizontal)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                    
                    Spacer()
                }
                .padding(.top)
            }
            .navigationTitle("Image Rendering Debug")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func createTestMessage() {
        debugInfo.removeAll()
        debugInfo.append("🚀 Starting test message creation...")
        
        let testURL = "https://www.amazon.com/dp/B08N5WRWNW"
        debugInfo.append("🔗 Test URL: \(testURL)")
        
        // Create message and log each step
        debugInfo.append("📝 Creating Message object...")
        let message = Message(text: testURL, categoryName: "Shopping")
        
        debugInfo.append("✅ Message created successfully")
        debugInfo.append("📊 Message details:")
        debugInfo.append("   - Main: \(message.mainMessage)")
        debugInfo.append("   - Sub: \(message.subcontent ?? "None")")
        debugInfo.append("   - Image: \(message.imageURL ?? "None")")
        debugInfo.append("   - Original: \(message.text)")
        
        // Check if it's detected as URL
        let isURL = testURL.lowercased().hasPrefix("http://") || testURL.lowercased().hasPrefix("https://")
        debugInfo.append("🔍 Detected as URL: \(isURL ? "✅ YES" : "❌ NO")")
        
        self.testMessage = message
        debugInfo.append("🎯 Test message set for display")
    }
}

/// Preview provider
struct ImageRenderingDebugView_Previews: PreviewProvider {
    static var previews: some View {
        ImageRenderingDebugView()
    }
}
