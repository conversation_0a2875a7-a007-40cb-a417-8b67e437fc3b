//
//  URLImageTestView.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import SwiftUI

/// Test view to demonstrate URL image functionality
struct URLImageTestView: View {
    @State private var testMessages: [Message] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 16) {
                    ForEach($testMessages) { $message in
                        ModernMessageView(message: $message) {
                            message.isCompleted.toggle()
                        }
                        .padding(.horizontal)
                    }
                }
                .padding(.top)
            }
            .navigationTitle("URL Image Test")
            .onAppear {
                loadTestMessages()
            }
        }
    }
    
    private func loadTestMessages() {
        let testURLs = [
            ("https://www.imdb.com/title/tt0133093/", "Movies"),
            ("https://www.amazon.com/dp/B08N5WRWNW", "Shopping"),
            ("https://www.nytimes.com/2023/12/01/technology/ai-chatbots.html", "News"),
            ("https://github.com/apple/swift", "Tech"),
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "Entertainment"),
            ("https://www.apple.com/iphone-15-pro/", "Tech"),
            ("https://www.netflix.com/title/70136120", "Movies"),
            ("https://www.spotify.com/us/", "Music")
        ]
        
        testMessages = testURLs.map { (url, category) in
            Message(text: url, categoryName: category)
        }
    }
}

/// Preview provider
struct URLImageTestView_Previews: PreviewProvider {
    static var previews: some View {
        URLImageTestView()
    }
}

/// Demo view showing URL parsing with and without images
struct URLParsingComparisonView: View {
    let testURL = "https://www.imdb.com/title/tt0133093/"
    let category = "Movies"
    
    var body: some View {
        VStack(spacing: 20) {
            Text("URL Parsing Comparison")
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Original URL:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Text(testURL)
                    .font(.caption)
                    .foregroundColor(.blue)
                    .lineLimit(1)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Parsed Message with Image:")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                let message = Message(text: testURL, categoryName: category)
                ModernMessageView(message: .constant(message)) { }
            }
            .padding()
            .background(Color.blue.opacity(0.1))
            .cornerRadius(8)
            
            Spacer()
        }
        .padding()
    }
}

/// Preview for comparison view
struct URLParsingComparisonView_Previews: PreviewProvider {
    static var previews: some View {
        URLParsingComparisonView()
    }
}
