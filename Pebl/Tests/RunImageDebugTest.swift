import Foundation

/// Simple test runner to execute our image debugging tests
class RunImageDebugTest {
    static func runAllImageTests() {
        print("🚀 Starting comprehensive image debugging tests...")
        print(String(repeating: "=", count: 60))
        
        // Run the quick image test first
        QuickImageTest.runQuickTest()
        
        print("\n" + String(repeating: "-", count: 60))
        print("✅ Image debugging tests completed!")
        print("Check the console output above for detailed results.")
        print(String(repeating: "=", count: 60))
    }
}
