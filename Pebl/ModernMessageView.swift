//
//  ModernMessageView.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import SwiftUI

/// Modern message view with square checkbox and clean main/sub content layout
struct ModernMessageView: View {
    @Binding var message: Message
    let onToggle: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            // Modern square checkbox
            ModernCheckbox(isChecked: message.isCompleted) {
                onToggle()
            }
            
            // Message content
            VStack(alignment: .leading, spacing: 4) {
                // Main message
                Text(message.mainMessage)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(message.isCompleted ? .secondary : .primary)
                    .strikethrough(message.isCompleted)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
                
                // Subcontent (if available)
                if let subcontent = message.subcontent, !subcontent.isEmpty {
                    Text(subcontent)
                        .font(.system(size: 14, weight: .regular))
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
            
            Spacer()
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .opacity(message.isCompleted ? 0.7 : 1.0)
        .animation(.easeInOut(duration: 0.2), value: message.isCompleted)
    }
}

/// Modern square checkbox component
struct ModernCheckbox: View {
    let isChecked: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: {
            action()
        }) {
            ZStack {
                // Background square
                RoundedRectangle(cornerRadius: 6)
                    .fill(isChecked ? Color.accentColor : Color(.systemBackground))
                    .frame(width: 24, height: 24)
                    .overlay(
                        RoundedRectangle(cornerRadius: 6)
                            .stroke(
                                isChecked ? Color.accentColor : Color(.systemGray3),
                                lineWidth: isChecked ? 0 : 1.5
                            )
                    )
                
                // Checkmark
                if isChecked {
                    Image(systemName: "checkmark")
                        .font(.system(size: 12, weight: .bold))
                        .foregroundColor(.white)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .scaleEffect(isChecked ? 1.1 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: isChecked)
    }
}

/// Modern message list view for categories
struct ModernMessageListView: View {
    @Binding var messages: [Message]
    let onMessageToggle: (Message) -> Void
    let onMessageDelete: (IndexSet) -> Void
    
    var body: some View {
        LazyVStack(spacing: 8) {
            ForEach($messages) { $message in
                ModernMessageView(message: $message) {
                    onMessageToggle(message)
                }
                .contextMenu {
                    Button("Delete", role: .destructive) {
                        if let index = messages.firstIndex(where: { $0.id == message.id }) {
                            onMessageDelete(IndexSet(integer: index))
                        }
                    }
                }
            }
        }
        .padding(.horizontal, 16)
    }
}

/// Preview and demo views
struct ModernMessageView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // Movie examples
            ModernMessageView(
                message: .constant(Message(
                    text: "Watch The Dark Knight recommended by John",
                    categoryName: "Movies to Watch"
                ))
            ) { }
            
            ModernMessageView(
                message: .constant(Message(
                    text: "See Inception on Netflix tonight",
                    categoryName: "Movies to Watch"
                ))
            ) { }
            
            // Shopping examples
            ModernMessageView(
                message: .constant(Message(
                    text: "Buy iPhone 15 Pro from Apple Store",
                    categoryName: "Shopping"
                ))
            ) { }
            
            ModernMessageView(
                message: .constant(Message(
                    text: "Get groceries for dinner party",
                    categoryName: "Shopping"
                ))
            ) { }
            
            // Book examples
            ModernMessageView(
                message: .constant(Message(
                    text: "Read Atomic Habits by James Clear",
                    categoryName: "To-Read"
                ))
            ) { }
            
            // Task examples
            ModernMessageView(
                message: .constant(Message(
                    text: "Complete quarterly report by Friday urgent",
                    categoryName: "To-Do"
                ))
            ) { }
            
            // Completed example
            ModernMessageView(
                message: .constant(Message(
                    text: "Schedule dentist appointment",
                    isCompleted: true,
                    categoryName: "To-Do"
                ))
            ) { }
        }
        .padding()
        .background(Color(.systemGroupedBackground))
        .previewLayout(.sizeThatFits)
    }
}

/// Demo view showing different parsing results
struct MessageParsingDemoView: View {
    let demoMessages = [
        ("Movies to Watch", [
            "Watch The Dark Knight recommended by John",
            "See Inception on Netflix tonight",
            "Rahul recommended Prestige",
            "Watch Parasite - Korean film",
            "The Godfather from IMDB top 250"
        ]),
        ("Shopping", [
            "Buy iPhone 15 Pro from Apple Store",
            "Get groceries for dinner party",
            "Order Nike shoes size 10",
            "Purchase book for book club",
            "Buy urgent: milk and bread"
        ]),
        ("To-Read", [
            "Read Atomic Habits by James Clear",
            "Finish 1984 for book club",
            "Start The Hobbit recommended by Sarah",
            "Read research paper on AI",
            "Book: Dune by Frank Herbert"
        ]),
        ("To-Do", [
            "Complete quarterly report by Friday urgent",
            "Schedule dentist appointment",
            "Call mom for birthday reminder",
            "Finish project deadline: Monday",
            "Important: submit tax documents"
        ])
    ]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(demoMessages, id: \.0) { categoryName, messages in
                    Section(categoryName) {
                        ForEach(messages, id: \.self) { messageText in
                            let message = Message(text: messageText, categoryName: categoryName)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                HStack {
                                    Text("Original:")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    Text(messageText)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                
                                ModernMessageView(message: .constant(message)) { }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
            }
            .navigationTitle("Message Parsing Demo")
        }
    }
}
