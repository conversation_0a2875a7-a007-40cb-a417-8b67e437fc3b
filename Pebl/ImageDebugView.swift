//
//  ImageDebugView.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import SwiftUI

/// Debug view to test image loading functionality
struct ImageDebugView: View {
    @State private var testMessages: [Message] = []
    @State private var debugInfo: [String] = []
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Debug info section
                    if !debugInfo.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Debug Info:")
                                .font(.headline)
                            
                            ForEach(debugInfo, id: \.self) { info in
                                Text(info)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 8)
                            }
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                    
                    // Test messages
                    LazyVStack(spacing: 16) {
                        ForEach($testMessages) { $message in
                            VStack(alignment: .leading, spacing: 8) {
                                Text("URL: \(message.text)")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                
                                Text("Image URL: \(message.imageURL ?? "None")")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                ModernMessageView(message: $message) {
                                    message.isCompleted.toggle()
                                }
                            }
                            .padding(.horizontal)
                        }
                    }
                }
                .padding(.top)
            }
            .navigationTitle("Image Debug")
            .onAppear {
                loadTestMessages()
            }
        }
    }
    
    private func loadTestMessages() {
        debugInfo.append("Starting image debug test...")
        
        let testURLs = [
            ("https://www.amazon.com/dp/B08N5WRWNW", "Shopping"),
            ("https://www.amazon.com/Apple-iPhone-15-Pro-Titanium/dp/B0CHX1W1XY", "Shopping"),
            ("https://www.imdb.com/title/tt0133093/", "Movies"),
            ("https://www.apple.com/iphone-15-pro/", "Tech"),
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "Entertainment")
        ]
        
        testMessages = testURLs.map { (url, category) in
            debugInfo.append("Creating message for: \(url)")
            let message = Message(text: url, categoryName: category)
            debugInfo.append("Image URL result: \(message.imageURL ?? "None")")
            return message
        }
        
        debugInfo.append("Test messages created: \(testMessages.count)")
    }
}

/// Direct image URL test view
struct DirectImageTestView: View {
    let testImageURLs = [
        "https://m.media-amazon.com/images/G/01/imdb/images/nopicture/medium/name-2135195744._CB466677075_.png",
        "https://via.placeholder.com/300x200/007ACC/FFFFFF?text=Shopping",
        "https://github.githubassets.com/images/modules/logos_page/GitHub-Mark.png",
        "https://www.apple.com/newsroom/images/product/iphone/standard/Apple_announce-iphone12pro_10132020.jpg.og.jpg"
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    ForEach(testImageURLs, id: \.self) { imageURL in
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Testing: \(imageURL)")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            URLImageView(
                                imageURL: imageURL,
                                width: 120,
                                height: 80
                            )
                        }
                        .padding()
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                    }
                }
                .padding(.top)
            }
            .navigationTitle("Direct Image Test")
        }
    }
}

/// Preview providers
struct ImageDebugView_Previews: PreviewProvider {
    static var previews: some View {
        ImageDebugView()
    }
}

struct DirectImageTestView_Previews: PreviewProvider {
    static var previews: some View {
        DirectImageTestView()
    }
}
