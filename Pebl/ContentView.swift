//
//  ContentView.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/4/25.
//

import SwiftUI


struct ContentView: View {
    @State private var inputMessage: String = ""
    @StateObject private var categoryManager = CategoryManager()
    @State private var selectedCategory: Category? = nil
    @State private var navigationStack: [Category] = []

    var body: some View {
        NavigationView {
            VStack {
                if let selectedCategory = selectedCategory {
                    CategoryDetailView(
                        category: selectedCategory,
                        categoryManager: categoryManager,
                        onBack: {
                            if navigationStack.isEmpty {
                                self.selectedCategory = nil
                            } else {
                                self.selectedCategory = navigationStack.removeLast()
                            }
                        },
                        onNavigateToSubcategory: { subcategory in
                            navigationStack.append(selectedCategory)
                            self.selectedCategory = subcategory
                        }
                    )
                } else {
                    // Main screen
                    VStack(spacing: 5) {
                        TextField("Enter your message here...", text: $inputMessage)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .padding()
                        
                        Button("Store") {
                            categorizeMessage(inputMessage)
                            inputMessage = ""
                        }
                        .font(.headline)
                        .padding()
                        .frame(width: 100, height: 50)
                        .background(Color.white)
                        .foregroundColor(.black)
                        .cornerRadius(10)
                        .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                        .padding(.bottom, 50)

                        ScrollView {
                            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                                ForEach(categoryManager.rootCategories.sorted(by: { $0.name < $1.name }), id: \.id) { category in
                                    Button(action: {
                                        self.selectedCategory = category
                                    }) {
                                        VStack {
                                            Image(systemName: category.sfSymbol.isEmpty ? "folder.fill" : category.sfSymbol)
                                                .resizable()
                                                .scaledToFit()
                                                .frame(width: 120, height: 40)
                                                .foregroundColor(.black)
                                            HStack {
                                                Text(category.name)
                                                    .foregroundColor(.black)
                                                Text("(\(category.getTotalMessageCount()))")
                                                    .font(.caption)
                                                    .foregroundColor(.gray)
                                            }
                                        }
                                        .frame(width: 180, height: 120)
                                        .background(Color.white)
                                        .cornerRadius(10)
                                        .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                                    }
                                }
                            }
                            .padding(.bottom, 20)

                            Button("Add Category") {
                                let alert = UIAlertController(title: "New Category", message: "Enter a name for the new category", preferredStyle: .alert)
                                alert.addTextField { textField in
                                    textField.placeholder = "Category name"
                                }
                                alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
                                    if let categoryName = alert.textFields?.first?.text, !categoryName.isEmpty {
                                        DispatchQueue.main.async {
                                            addNewCategory(categoryName)
                                        }
                                    }
                                })
                                alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

                                if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let window = scene.windows.first,
                                   let rootVC = window.rootViewController {
                                    rootVC.present(alert, animated: true, completion: nil)
                                }
                            }
                            .font(.headline)
                            .padding()
                            .frame(width: 150, height: 50)
                            .background(Color.white)
                            .foregroundColor(.black)
                            .cornerRadius(10)
                            .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                            .padding(.bottom, 40)
                        }
                    }
                    .navigationTitle("Pebl")
                }
            }
            .onAppear {
                DispatchQueue.main.async {
                    loadCategoriesFromFile()
                }
            }
        }
    }

    private func categorizeMessage(_ message: String) {
        guard !message.isEmpty else { return }

        inputMessage = "" // Clear input immediately for better UX

        // Use the new centralized categorization system
        categoryManager.addMessage(message) { success in
            if success {
                self.saveCategoriesToFile()
            } else {
                print("⚠️ Failed to categorize message: \(message)")
            }
        }
    }

    private func addNewCategory(_ newCategory: String) {
        guard !newCategory.isEmpty else { return }

        // Create category with default icon first for immediate UI feedback
        let category = categoryManager.addRootCategory(name: newCategory, sfSymbol: "folder.fill")
        saveCategoriesToFile()

        // Get appropriate SF symbol in background
        DispatchQueue.global(qos: .userInitiated).async {
            let aiModel = AIModel()
            let sfSymbol = aiModel.getSFSymbolForCategory(newCategory)

            DispatchQueue.main.async {
                category.sfSymbol = sfSymbol
                self.saveCategoriesToFile()
            }
        }
    }

    // This function is now handled in CategoryDetailView

    private func saveCategoriesToFile() {
        let fileManager = FileManager.default
        if let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent("categories.json")
            do {
                let data = try JSONEncoder().encode(categoryManager.rootCategories)
                try data.write(to: fileURL)
            } catch {
                print("Error saving categories: \(error)")
            }
        }
    }

    private func loadCategoriesFromFile() {
        let fileManager = FileManager.default
        if let documentDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first {
            let fileURL = documentDirectory.appendingPathComponent("categories.json")
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    let data = try Data(contentsOf: fileURL)

                    // Try to decode as new format first
                    if let loadedCategories = try? JSONDecoder().decode([Category].self, from: data) {
                        categoryManager.rootCategories = loadedCategories
                        // Set categoryManager reference for all loaded categories
                        setCategoryManagerReferences(for: loadedCategories)
                        return
                    }

                    // If that fails, try to decode as old format and migrate
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let oldCategories = json["categories"] as? [String: [String]],
                       let oldCategoryImages = json["categoryImages"] as? [String: String] {

                        // Migrate old format to new format
                        categoryManager.rootCategories = []
                        for (categoryName, messageStrings) in oldCategories {
                            let sfSymbol = oldCategoryImages[categoryName] ?? "folder.fill"
                            let category = categoryManager.addRootCategory(name: categoryName, sfSymbol: sfSymbol)
                            for messageString in messageStrings {
                                category.addMessage(messageString)
                            }
                        }

                        // Save in new format
                        saveCategoriesToFile()
                        print("Successfully migrated categories from old format to new format")
                        return
                    }

                    print("Could not decode categories file in any known format")

                } catch {
                    print("Error loading categories: \(error)")
                    // Keep default categories if loading fails
                }
            }
            // Default categories are already loaded in CategoryManager init
        }
    }

    /// Set categoryManager references for loaded categories (recursive)
    private func setCategoryManagerReferences(for categories: [Category]) {
        for category in categories {
            category.categoryManager = categoryManager
            setCategoryManagerReferences(for: category.subcategories)
        }
    }


}

// MARK: - Category Detail View

struct CategoryDetailView: View {
    @ObservedObject var category: Category
    @ObservedObject var categoryManager: CategoryManager
    let onBack: () -> Void
    let onNavigateToSubcategory: (Category) -> Void

    var body: some View {
        VStack {
            // Messages in this category (first half)
            if !category.messages.isEmpty {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        let halfCount = max(1, category.messages.count / 2)
                        ForEach(Array($category.messages.prefix(halfCount))) { $message in
                            ModernMessageView(message: $message) {
                                category.toggleMessageCompletion(withId: message.id)
                            }
                            .contextMenu {
                                Button("Delete", role: .destructive) {
                                    category.removeMessage(withId: message.id)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                }
                .frame(maxHeight: 200) // Limit height to make room for subcategories
            }

            // Subcategories - positioned in the middle
            if !category.subcategories.isEmpty {
                Text("Subcategories")
                    .font(.headline)
                    .padding(.top, 20)

                LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())]) {
                    ForEach(category.subcategories.sorted(by: { $0.name < $1.name }), id: \.id) { subcategory in
                        Button(action: {
                            onNavigateToSubcategory(subcategory)
                        }) {
                            VStack {
                                Image(systemName: subcategory.sfSymbol.isEmpty ? "folder.fill" : subcategory.sfSymbol)
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 120, height: 40)
                                    .foregroundColor(.black)
                                HStack {
                                    Text(subcategory.name)
                                        .foregroundColor(.black)
                                    Text("(\(subcategory.getTotalMessageCount()))")
                                        .font(.caption)
                                        .foregroundColor(.gray)
                                }
                            }
                            .frame(width: 180, height: 120)
                            .background(Color.white)
                            .cornerRadius(10)
                            .shadow(color: Color.gray.opacity(0.5), radius: 5, x: 0, y: 5)
                        }
                    }
                }
                .padding(.bottom, 20)
            }

            // Remaining messages (second half)
            if !category.messages.isEmpty {
                ScrollView {
                    LazyVStack(spacing: 8) {
                        let halfCount = max(1, category.messages.count / 2)
                        ForEach(Array($category.messages.dropFirst(halfCount))) { $message in
                            ModernMessageView(message: $message) {
                                category.toggleMessageCompletion(withId: message.id)
                            }
                            .contextMenu {
                                Button("Delete", role: .destructive) {
                                    category.removeMessage(withId: message.id)
                                }
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.top, 8)
                }

                // Modern clear completed messages button
                if category.messages.contains(where: { $0.isCompleted }) {
                    Button(action: {
                        category.removeCompletedMessages()
                    }) {
                        HStack(spacing: 6) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 14))
                            Text("Clear Completed")
                                .font(.system(size: 14, weight: .medium))
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(
                            LinearGradient(
                                gradient: Gradient(colors: [Color.orange, Color.orange.opacity(0.8)]),
                                startPoint: .leading,
                                endPoint: .trailing
                            )
                        )
                        .cornerRadius(20)
                        .shadow(color: Color.orange.opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(.bottom, 16)
                }
            }

            Spacer()
                .frame(height: 40)
        }
        .navigationTitle(category.name)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button("Back") {
                    onBack()
                }
                .foregroundColor(.black)
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button("Add Subcategory") {
                        showAddSubcategoryAlert()
                    }

                    Button("Delete Category", role: .destructive) {
                        showDeleteCategoryAlert()
                    }
                } label: {
                    Image(systemName: "ellipsis")
                        .foregroundColor(.black)
                        .font(.title2)
                }
            }
        }
    }

    private func showAddSubcategoryAlert() {
        let alert = UIAlertController(title: "New Subcategory", message: "Enter a name for the new subcategory", preferredStyle: .alert)
        alert.addTextField { textField in
            textField.placeholder = "Subcategory name"
        }
        alert.addAction(UIAlertAction(title: "Add", style: .default) { _ in
            if let subcategoryName = alert.textFields?.first?.text, !subcategoryName.isEmpty {
                // Use the new system that includes recategorization
                category.addSubcategoryWithRecategorization(name: subcategoryName, sfSymbol: "folder.fill") { result in
                    if result.success {
                        // Get appropriate SF symbol in background
                        DispatchQueue.global(qos: .userInitiated).async {
                            let aiModel = AIModel()
                            let sfSymbol = aiModel.getSFSymbolForCategory(subcategoryName)

                            DispatchQueue.main.async {
                                result.subcategory.sfSymbol = sfSymbol

                                // Show recategorization results if any messages were moved
                                if !result.recategorization.movedMessages.isEmpty {
                                    showRecategorizationResults(result.recategorization)
                                }
                            }
                        }
                    } else {
                        print("⚠️ Failed to add subcategory: \(subcategoryName)")
                    }
                }
            }
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    private func showDeleteCategoryAlert() {
        let alert = UIAlertController(title: "Delete Category", message: "Do you want to distribute the contents to other categories?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Yes", style: .default) { _ in
            DispatchQueue.main.async {
                let aiModel = AIModel()
                category.redistributeMessages(using: aiModel, to: categoryManager.rootCategories)

                // Remove this category from its parent or root
                if let parent = category.parent {
                    parent.removeSubcategory(category)
                } else {
                    categoryManager.removeRootCategory(category)
                }
                onBack()
            }
        })
        alert.addAction(UIAlertAction(title: "No, delete all", style: .destructive) { _ in
            if let parent = category.parent {
                parent.removeSubcategory(category)
            } else {
                categoryManager.removeRootCategory(category)
            }
            onBack()
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }

    /// Show recategorization results to the user
    private func showRecategorizationResults(_ result: RecategorizationResult) {
        let movedCount = result.movedMessages.count
        let newCategoriesCount = result.newCategoriesCreated.count

        var message = "Recategorization completed!\n"
        message += "• \(movedCount) messages moved to better categories\n"
        message += "• \(result.skippedCount) messages stayed in place\n"

        if newCategoriesCount > 0 {
            message += "• \(newCategoriesCount) new categories created: \(result.newCategoriesCreated.joined(separator: ", "))"
        }

        let alert = UIAlertController(title: "Smart Recategorization", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default, handler: nil))

        if let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = scene.windows.first,
           let rootVC = window.rootViewController {
            rootVC.present(alert, animated: true, completion: nil)
        }
    }
}

#Preview {
    ContentView()
}
