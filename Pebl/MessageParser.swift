//
//  MessageParser.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import Foundation

/// Parses messages to extract clean main content and subcontent for modern UI display
class MessageParser {
    
    struct ParsedMessage {
        let mainMessage: String
        let subcontent: String?
        let originalMessage: String
    }
    
    // MARK: - Main Parsing Function
    
    static func parseMessage(_ message: String, for categoryType: String) -> ParsedMessage {
        let cleanMessage = message.trimmingCharacters(in: .whitespacesAndNewlines)
        
        switch categoryType.lowercased() {
        case let category where category.contains("movie") || category.contains("film") || category.contains("watch"):
            return parseMovieMessage(cleanMessage)
        case let category where category.contains("book") || category.contains("read"):
            return parseBookMessage(cleanMessage)
        case let category where category.contains("shop") || category.contains("buy"):
            return parseShoppingMessage(cleanMessage)
        case let category where category.contains("todo") || category.contains("to-do") || category.contains("task"):
            return parseTaskMessage(cleanMessage)
        case let category where category.contains("appointment") || category.contains("schedule"):
            return parseAppointmentMessage(cleanMessage)
        default:
            return parseGenericMessage(cleanMessage)
        }
    }
    
    // MARK: - Category-Specific Parsers
    
    private static func parseMovieMessage(_ message: String) -> ParsedMessage {
        let redundantWords = ["watch", "see", "movie", "film", "tonight", "later", "today", "tomorrow"]
        let contextPatterns = [
            ("recommended by", "Recommended by"),
            ("suggested by", "Suggested by"),
            ("from", "From"),
            ("on netflix", "On Netflix"),
            ("on amazon", "On Amazon Prime"),
            ("on disney", "On Disney+"),
            ("on hulu", "On Hulu"),
            ("on hbo", "On HBO"),
            ("in theaters", "In theaters"),
            ("rated", "Rated"),
            ("genre:", "Genre:"),
            ("year:", "Year:"),
            ("director:", "Director:")
        ]
        
        return parseWithPatterns(message, redundantWords: redundantWords, contextPatterns: contextPatterns)
    }
    
    private static func parseBookMessage(_ message: String) -> ParsedMessage {
        let redundantWords = ["read", "book", "novel", "finish", "start", "continue"]
        let contextPatterns = [
            ("recommended by", "Recommended by"),
            ("suggested by", "Suggested by"),
            ("by author", "By"),
            ("author:", "Author:"),
            ("genre:", "Genre:"),
            ("pages:", "Pages:"),
            ("published:", "Published:"),
            ("for book club", "For book club"),
            ("assignment", "Assignment")
        ]
        
        return parseWithPatterns(message, redundantWords: redundantWords, contextPatterns: contextPatterns)
    }
    
    private static func parseShoppingMessage(_ message: String) -> ParsedMessage {
        let redundantWords = ["buy", "get", "purchase", "order", "need", "pick up"]
        let contextPatterns = [
            ("for", "For"),
            ("from", "From"),
            ("at", "At"),
            ("online", "Online"),
            ("in store", "In store"),
            ("urgent", "Urgent"),
            ("sale", "On sale"),
            ("discount", "Discounted"),
            ("quantity:", "Qty:"),
            ("size:", "Size:"),
            ("color:", "Color:")
        ]
        
        return parseWithPatterns(message, redundantWords: redundantWords, contextPatterns: contextPatterns)
    }
    
    private static func parseTaskMessage(_ message: String) -> ParsedMessage {
        let redundantWords = ["do", "complete", "finish", "task", "todo", "need to"]
        let contextPatterns = [
            ("by", "Due"),
            ("before", "Before"),
            ("urgent", "Urgent"),
            ("important", "Important"),
            ("deadline:", "Deadline:"),
            ("priority:", "Priority:"),
            ("for work", "Work"),
            ("for home", "Personal"),
            ("reminder", "Reminder")
        ]
        
        return parseWithPatterns(message, redundantWords: redundantWords, contextPatterns: contextPatterns)
    }
    
    private static func parseAppointmentMessage(_ message: String) -> ParsedMessage {
        //let redundantWords = ["schedule", "book", "appointment", "meeting"]
        let redundantWords = [String]()
        let contextPatterns = [
            ("with", "With"),
            ("at", "At"),
            ("on", "On"),
            ("for", "For"),
            ("doctor", "Doctor"),
            ("dentist", "Dentist"),
            ("urgent", "Urgent"),
            ("followup", "Follow-up"),
            ("annual", "Annual"),
            ("checkup", "Checkup")
        ]
        
        return parseWithPatterns(message, redundantWords: redundantWords, contextPatterns: contextPatterns)
    }
    
    private static func parseGenericMessage(_ message: String) -> ParsedMessage {
        // For generic messages, try to extract any contextual information
        let contextPatterns = [
            ("recommended by", "Recommended by"),
            ("suggested by", "Suggested by"),
            ("from", "From"),
            ("for", "For"),
            ("urgent", "Urgent"),
            ("important", "Important"),
            ("note:", "Note:")
        ]
        
        return parseWithPatterns(message, redundantWords: [], contextPatterns: contextPatterns)
    }
    
    // MARK: - Helper Functions
    
    private static func parseWithPatterns(_ message: String, redundantWords: [String], contextPatterns: [(String, String)]) -> ParsedMessage {
        var workingMessage = message
        var subcontent: String?
        
        // Extract context information first
        for (pattern, label) in contextPatterns {
            if let contextInfo = extractContext(from: workingMessage, pattern: pattern, label: label) {
                subcontent = contextInfo.context
                workingMessage = contextInfo.remainingMessage
                break
            }
        }
        
        // Clean redundant words
        let mainMessage = cleanRedundantWords(workingMessage, redundantWords: redundantWords)
        
        return ParsedMessage(
            mainMessage: mainMessage.isEmpty ? message : mainMessage,
            subcontent: subcontent,
            originalMessage: message
        )
    }
    
    private static func extractContext(from message: String, pattern: String, label: String) -> (context: String, remainingMessage: String)? {
        let messageLower = message.lowercased()
        let patternLower = pattern.lowercased()
        
        if let range = messageLower.range(of: patternLower) {
            let startIndex = range.upperBound
            let contextPart = String(message[startIndex...]).trimmingCharacters(in: .whitespacesAndNewlines)
            let mainPart = String(message[..<range.lowerBound]).trimmingCharacters(in: .whitespacesAndNewlines)
            
            if !contextPart.isEmpty {
                let formattedContext = "\(label) \(contextPart)"
                return (formattedContext, mainPart)
            }
        }
        
        return nil
    }
    
    private static func cleanRedundantWords(_ message: String, redundantWords: [String]) -> String {
        var words = message.components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        // Remove redundant words from the beginning
        while !words.isEmpty && redundantWords.contains(words.first!.lowercased()) {
            words.removeFirst()
        }
        
        // Remove redundant words from the end
        while !words.isEmpty && redundantWords.contains(words.last!.lowercased()) {
            words.removeLast()
        }
        
        let result = words.joined(separator: " ").trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Capitalize first letter
        return result.isEmpty ? result : result.prefix(1).uppercased() + result.dropFirst()
    }
}

// MARK: - Extensions

extension MessageParser {
    
    /// Quick parse for display purposes
    static func quickParse(_ message: String) -> (main: String, sub: String?) {
        let parsed = parseGenericMessage(message)
        return (parsed.mainMessage, parsed.subcontent)
    }
    
    /// Parse with category context
    static func parseForCategory(_ message: String, categoryName: String) -> ParsedMessage {
        return parseMessage(message, for: categoryName)
    }
}
